# UserId 可空修复方案

## 问题解决
已将 `userId` 字段设置为可空，彻底解决 NOT NULL 约束错误。

## 修改内容

### 1. `backend/src/app/slides/slide.entity.ts`
```typescript
// 修改前
@Column()
userId!: number;

// 修改后
@Column({ nullable: true })
userId?: number;
```

### 2. `backend/src/app/slides/slides.service.ts`
```typescript
// 修改前
async createSlide(userId: number, createSlideDto: CreateSlideDto, file?: MulterFile)

// 修改后
async createSlide(userId: number | undefined, createSlideDto: CreateSlideDto, file?: MulterFile)
```

### 3. `backend/src/app/slides/slide.repository.ts`
```typescript
// 修改前
async findByUserId(userId: number, visibility: 'public' | 'private' | 'all' = 'all')

// 修改后
async findByUserId(userId: number | undefined, visibility: 'public' | 'private' | 'all' = 'all')
```

## 影响说明

### 正面影响
- ✅ 彻底解决了 userId NOT NULL 约束错误
- ✅ 允许创建匿名幻灯片（无用户关联）
- ✅ 系统更加灵活，支持未登录用户创建内容

### 注意事项
- 📝 部分幻灯片可能没有关联用户
- 📝 查询时需要处理 userId 为空的情况
- 📝 权限控制需要考虑匿名用户场景

## 测试步骤
1. 重启服务：`npm run dev`
2. 尝试创建幻灯片（无论是否登录）
3. 应该能成功创建并进入大纲生成阶段

## 后续优化建议
- 考虑为匿名用户添加会话管理
- 实现匿名幻灯片的临时存储机制
- 添加匿名转注册用户的数据迁移功能
