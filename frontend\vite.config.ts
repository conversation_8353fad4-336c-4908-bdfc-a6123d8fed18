import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
    return {
        plugins: [vue()],
        resolve: {
            alias: {
                '@': resolve(__dirname, './src')
            },
        },
        server: {
            host: process.env.VITE_DEV_HOST || '0.0.0.0', // 允许外部访问
            port: parseInt(process.env.VITE_DEV_PORT || '3000'),
            proxy: {
                '/uploads': {
                    target: process.env.VITE_UPLOADS_BASE_URL || 'http://localhost:3001/uploads',
                    changeOrigin: true,
                    // 不需要重写路径，直接透传
                }
            }
        }
    }
})