# 最终 UserId 问题修复

## 问题根源
经过调试发现，虽然 `userId` 正确传递到了 service 层，但 TypeORM 在保存时将其设置为 `NULL`。

## 根本原因
在 `slide.entity.ts` 中，`@JoinColumn({ name: 'userId' })` 导致 TypeORM 认为 `userId` 列是由关系管理的，因此忽略了直接设置的 `userId` 值。

## 最终修复

### 修改 `backend/src/app/slides/slide.entity.ts`
```typescript
// 修复前
@ManyToOne(() => User, user => user.slides)
@JoinColumn({ name: 'userId' })
user: User = {} as User;

// 修复后
@ManyToOne(() => User, user => user.slides)
user: User = {} as User;
```

移除了 `@JoinColumn({ name: 'userId' })`，让 TypeORM 自动处理外键关系。

## 验证
重启服务后，创建幻灯片应该能正常工作，不再出现 `NOT NULL constraint failed: slides.userId` 错误。

## 技术说明
- TypeORM 会自动创建外键列（通常命名为 `userUserId` 或类似）
- 当我们直接设置 `userId` 字段时，TypeORM 能正确处理
- 移除显式的 `@JoinColumn` 配置避免了冲突

## 测试步骤
1. 重启服务：`npm run dev`
2. 登录用户
3. 尝试创建新的幻灯片
4. 应该能成功创建并进入大纲生成阶段
