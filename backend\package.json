{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "type": "commonjs", "scripts": {"build": "tsc -p tsconfig.simple.json && tsc-alias -p tsconfig.simple.json", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node dist/main.js", "start:dev": "node --loader ts-node/esm src/main.ts", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --loader ts-node/esm --experimental-specifier-resolution=node -r tsconfig-paths/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "init-db": "node --loader ts-node/esm src/init-db.ts", "dev": "nest start --watch", "create-admin": "ts-node src/scripts/create-admin.ts"}, "dependencies": {"@fastify/static": "^8.2.0", "@nestjs/axios": "^4.0.1", "@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.5", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.5", "@nestjs/schedule": "^6.0.0", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@types/multer": "^2.0.0", "async-mutex": "^0.5.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cookie-parser": "^1.4.7", "http-proxy": "^1.18.1", "morgan": "^1.10.1", "multer": "^2.0.2", "openmcp-sdk": "^0.1.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "puppeteer": "^24.16.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "sqlite3": "^5.1.7", "swagger-ui-express": "^5.0.1", "tree-kill": "^1.2.2", "typeorm": "^0.3.25", "uuid": "^11.1.0", "wait-on": "^8.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.10", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.5", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.9", "@types/express": "^5.0.3", "@types/fs-extra": "^11.0.4", "@types/http-proxy": "^1.17.16", "@types/jest": "^29.5.14", "@types/morgan": "^1.9.10", "@types/node": "^22.16.5", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@types/wait-on": "^5.3.4", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.1.4", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}