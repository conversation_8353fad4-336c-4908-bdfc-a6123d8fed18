# CORS 跨域问题解决方案

## 问题描述
当前遇到的错误：
```
Access to XMLHttpRequest at 'http://localhost:3001/api/auth/login' from origin 'http://*************:3000' has been blocked by CORS policy
```

## 已修复的内容

### 1. 后端 CORS 配置
已在 `backend/src/main.ts` 中添加了 CORS 配置，支持：
- `http://localhost:3000`
- `http://127.0.0.1:3000`  
- `http://*************:3000`
- 其他局域网 IP 地址的动态匹配

### 2. 前端环境变量配置
创建了以下配置文件：
- `frontend/.env` - 默认配置
- `frontend/.env.local` - 本地开发配置（可自定义）

### 3. Vite 配置优化
更新了 `frontend/vite.config.ts`，支持：
- 从环境变量读取主机和端口配置
- 允许外部设备访问 (`host: '0.0.0.0'`)

## 解决步骤

### 方案一：重启服务（推荐）
```bash
# 停止当前服务（Ctrl+C）
# 然后重新启动
npm run dev
```

### 方案二：如果仍有问题，配置本地 IP
1. 编辑 `frontend/.env.local` 文件
2. 将您的实际 IP 地址替换到配置中：
```env
VITE_API_BASE_URL=http://*************:3001/api
VITE_UPLOADS_BASE_URL=http://*************:3001/uploads
```

### 方案三：检查防火墙
确保端口 3000 和 3001 没有被防火墙阻止。

## 验证修复
重启后，访问 http://*************:3000 应该能正常工作，不再出现 CORS 错误。

## 注意事项
- `.env.local` 文件已添加到 `.gitignore`，不会被提交到版本控制
- 如果在不同网络环境下开发，只需修改 `.env.local` 中的 IP 地址即可
