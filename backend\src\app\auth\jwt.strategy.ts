import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { UserRepository } from '@/app/users/users.repository';
import { Request } from 'express';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
    constructor(private userRepository: UserRepository) {
        const jwtSecret = process.env.JWT_SECRET || 'defaultSecretKey';
        console.log('JWT Strategy initialized with secret:', jwtSecret);

        super({
            // 自定义从 Cookie 提取 JWT
            jwtFromRequest: ExtractJwt.fromExtractors([
                (request: Request) => {
                    const token = request.cookies?.jwt;
                    console.log('Extracting JWT from cookie:', token ? 'found' : 'not found');
                    return token;
                },
            ]),
            ignoreExpiration: false,
            secretOrKey: jwtSecret,
        });
    }

    async validate(payload: any) {
        return await this.userRepository.findOneById(payload.sub);
    }
}