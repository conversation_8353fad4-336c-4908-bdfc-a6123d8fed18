// vite.config.ts
import { defineConfig } from "file:///E:/1work/open_source/slidev-ai/node_modules/vite/dist/node/index.js";
import vue from "file:///E:/1work/open_source/slidev-ai/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import { resolve } from "path";
var __vite_injected_original_dirname = "E:\\1work\\open_source\\slidev-ai\\frontend";
var vite_config_default = defineConfig(({ mode }) => {
  return {
    plugins: [vue()],
    resolve: {
      alias: {
        "@": resolve(__vite_injected_original_dirname, "./src")
      }
    },
    server: {
      host: process.env.VITE_DEV_HOST || "0.0.0.0",
      // 允许外部访问
      port: parseInt(process.env.VITE_DEV_PORT || "3000"),
      proxy: {
        "/uploads": {
          target: process.env.VITE_UPLOADS_BASE_URL || "http://localhost:3001/uploads",
          changeOrigin: true
          // 不需要重写路径，直接透传
        }
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
