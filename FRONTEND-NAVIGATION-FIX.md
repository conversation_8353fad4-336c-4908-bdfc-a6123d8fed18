# 前端页面跳转问题修复

## 问题描述
点击"继续到大纲"按钮后，API调用成功（返回201状态码），但页面没有跳转到大纲阶段。

## 根本原因
1. **路由参数解析问题** - `parseInt(route.query.id + '')` 在 `id` 为 `undefined` 时返回 `NaN`
2. **响应数据处理** - 前端需要正确处理后端返回的 slide 对象
3. **调试信息缺失** - 缺少足够的日志来诊断问题

## 修复内容

### 1. `frontend/src/views/slides/process/index.vue`
```typescript
// 修复前
const id = computed(() => parseInt(route.query.id + ''));

// 修复后
const id = computed(() => {
    const idParam = route.query.id;
    return idParam ? parseInt(idParam as string) : undefined;
});
```

### 2. `frontend/src/views/slides/process/Stage1UserInput.vue`
```typescript
// 修复前
const res = await slidesStore.createSlide(formData);
emit('complete', res.data.id);

// 修复后
const res = await slidesStore.createSlide(formData);
const slideId = res.data.id;
if (slideId) {
    emit('complete', slideId);
} else {
    console.error('No slide ID in response');
    error.value = t('process.input.error.create-failed');
}
```

### 3. 添加调试日志
在关键位置添加了 `console.log` 来帮助诊断问题：
- API 响应数据
- slide ID 提取
- 页面跳转逻辑

## 验证步骤
1. 重启前端服务
2. 打开浏览器开发者工具的控制台
3. 尝试创建新的幻灯片
4. 查看控制台输出，确认：
   - API 调用成功
   - slide ID 正确提取
   - `handleStageComplete` 被调用
   - 页面跳转到大纲阶段

## 预期结果
- ✅ API 调用成功（201状态码）
- ✅ 正确提取 slide ID
- ✅ 页面自动跳转到大纲生成阶段
- ✅ URL 更新为 `/slides/process?id=<slideId>&stage=outline`

## 后续优化
- 移除调试日志（生产环境）
- 添加更好的错误处理
- 考虑添加加载状态指示器
