# 数据库重置修复方案

## 问题描述
TypeORM 在插入数据时仍然将 userId 设为 NULL，尽管我们已经修复了类型定义。

## 解决方案
1. **强制重新创建数据库** - 添加了 `dropSchema: true` 到 TypeORM 配置
2. **修改 Repository 创建方法** - 使用直接赋值而不是 TypeORM 的 create 方法

## 修改内容

### 1. `backend/src/app.module.ts`
```typescript
TypeOrmModule.forRoot({
    type: 'sqlite',
    database: 'database.sqlite',
    autoLoadEntities: true,
    synchronize: true,
    dropSchema: true, // 临时添加，强制重新创建数据库
}),
```

### 2. `backend/src/app/slides/slide.repository.ts`
```typescript
async create(slide: Partial<Slide>): Promise<Slide> {
    // 直接使用 save 方法，避免 create 方法的问题
    const newSlide = new Slide();
    Object.assign(newSlide, slide);
    return this.slideRepository.save(newSlide);
}
```

## 重要提醒
⚠️ **`dropSchema: true` 会删除所有现有数据！**

这是一个临时解决方案，用于解决 TypeORM 的外键关系问题。

## 测试步骤
1. 重启服务：`npm run dev`
2. 数据库将被完全重新创建
3. 重新注册用户
4. 尝试创建幻灯片

## 后续清理
测试成功后，应该移除 `dropSchema: true` 配置，因为它会在每次启动时删除所有数据。
