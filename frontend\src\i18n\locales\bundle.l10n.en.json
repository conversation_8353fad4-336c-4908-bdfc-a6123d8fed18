{"info.public-slide.no-public-slide": "No public slides found", "info.public-slide.created-at": "Created at:", "info.public-slide.updated-at": "Updated at:", "info.public-slide.check-back-later": "Check back later for public presentations.", "common.loading.slides": "Loading slides...", "common.error.fetch-slides": "Failed to fetch slides", "common.user.anonymous": "Anonymous", "common.user.profile-title": "User Profile", "dashboard.button.new": "New Slide", "dashboard.button.create": "Create Slide", "dashboard.filter.placeholder": "Filter", "dashboard.filter.all": "All", "dashboard.filter.public": "Public", "dashboard.filter.private": "Private", "dashboard.delete.confirm.message": "Are you sure you want to delete \\\"{title}\\\"?", "dashboard.delete.confirm.header": "Delete Confirmation", "dashboard.toast.delete.success.summary": "Deleted", "dashboard.toast.delete.success.detail": "Slide deleted successfully", "dashboard.toast.delete.error.summary": "Error", "dashboard.toast.delete.error.detail": "Failed to delete slide", "dashboard.empty.welcome.title": "Welcome to Slidev AI", "dashboard.empty.none-found.title": "No slides found", "dashboard.empty.all.desc": "Create your first presentation now.", "dashboard.empty.public.desc": "You have no public slides yet.", "dashboard.empty.private.desc": "You have no private slides yet.", "dashboard.tag.public": "Public", "dashboard.tag.private": "Private", "dashboard.status.completed": "Completed", "dashboard.status.processing": "Processing", "dashboard.action.edit": "Edit", "dashboard.action.delete": "Delete", "preview.loading.slide": "Loading slide...", "preview.error.load-slide": "Failed to load slide", "preview.button.copy-markdown": "<PERSON><PERSON>", "preview.instructions.title": "How to use this Slidev presentation:", "preview.instructions.step1": "Copy the markdown content above", "preview.instructions.step2": "Save it as a .md file (e.g., presentation.md)", "preview.instructions.step3": "Install Slidev:", "preview.instructions.step4": "Run:", "preview.not-found": "Slide not found", "auth.tagline": "AI-powered presentation creation tool", "auth.login.title": "<PERSON><PERSON>", "auth.login.username": "Username", "auth.login.password": "Password", "auth.login.button": "<PERSON><PERSON>", "auth.login.error": "<PERSON><PERSON> failed", "auth.login.prompt.no-account": "Don't have an account?", "auth.login.prompt.register-link": "Register", "auth.register.title": "Register", "auth.register.username": "Username", "auth.register.email": "Email", "auth.register.password": "Password", "auth.register.button": "Register", "auth.register.error": "Registration failed", "auth.register.prompt.has-account": "Already have an account?", "auth.register.prompt.login-link": "<PERSON><PERSON>", "auth.logout.button": "Logout", "process.input.header": "Enter the basic information for your presentation", "process.input.slide-title": "Slide Title", "process.input.title.example": "i18n haru 功能", "process.input.slide-title.placeholder": "Enter slide title", "process.input.slide-content": "Slide Content", "process.input.add-example": "Add Example", "process.input.slide-content.placeholder": "Enter slide title", "process.input.slide-content.example": "I have recently been researching how to use i18n haru. Below are some materials and resources I have gathered:\n1. Official website: https://document.kirigaya.cn/blogs/i18n-haru/main.html\n2. Basic concepts of i18n: https://document.kirigaya.cn/docs/i18n-haru/introduction.html\n3. Basic concepts of i18n messages: https://document.kirigaya.cn/docs/i18n-haru/introduction.message.html\nBased on these materials, please help me design a presentation introducing the i18n haru plugin.", "process.input.slide-content.help": "Enter your slide content, any text fine.", "process.input.upload.label": "Upload File (Optional)", "process.input.upload.selected": "Selected file:", "process.input.upload.help": "Upload a PDF, Word document, or Markdown file as source material (optional, max 5MB)", "process.input.visibility": "Visibility", "process.input.visibility.placeholder": "Select visibility", "process.input.visibility.help": "Choose whether your slide is public or private.", "process.input.cancel": "Cancel", "process.input.save-draft": "Save Draft", "process.input.saving": "Creating...", "process.input.continue": "Continue to Outline", "process.input.error.title-required": "Please enter a slide title", "process.input.error.content-required": "Please provide either a slide content or upload a file", "process.input.error.auth": "Authentication failed. Please log in again.", "process.input.error.create-failed": "Failed to create slide. Please try again.", "process.input.error.network": "Network error. Please check your connection and try again.", "process.input.success.save": "Slide saved successfully", "process.input.success.load": "Slide loaded successfully", "process.input.error.load": "Failed to load slide:", "process.input.success": "Success", "process.input.error": "Error", "process.input.save-failed": "Save Failed", "process.outline.title": "Presentation Outline Generator", "process.outline.generating": "Generating outline...", "process.outline.generating.help": "Please wait while we process your presentation", "process.outline.generated": "Generated Outline", "process.outline.processing-completed-no-outline": "Processing completed but no outline was generated.", "process.outline.try-again": "Try Again", "process.outline.cancel": "Cancel", "process.outline.save-draft": "Save Draft", "process.outline.continue": "Continue to Markdown", "process.outline.save-success": "Save Outlines Successfully", "process.outline.save-error": "Failed to save outlines:", "process.outline.loaded-existing": "Loaded Existing Outline", "process.outline.using-existing": "Using previously generated outline", "process.outline.error.invalid-id": "Invalid slide ID", "process.outline.error.fetch-failed": "Failed to fetch slide data", "process.outline.error.parse": "Error parsing existing outlines:", "process.outline.error.init-conn": "Failed to initialize connection", "process.outline.error.sse": "Failed to establish connection after multiple attempts. Please try again later.", "process.outline.connection-issue": "Connection Issue", "process.outline.connection-failed": "Connection Failed", "process.outline.processing-complete": "Processing Complete", "process.outline.finished": "Outline generation finished successfully", "process.outline.slide-busy": "Slide Busy", "process.outline.tool-response": "Tool response received", "process.outline.error.unknown": "Unknown error", "process.outline.error.failed-parse": "Failed to parse server message", "process.outline.error.unknown-tool": "Unknown tool", "process.outline.error": "Error", "process.markdown.title": "Markdown Generator", "process.markdown.generating": "Generating markdown...", "process.markdown.generating.help": "Please wait while we process your presentation", "process.markdown.processing-complete": "Processing Complete", "process.markdown.finished": "Markdown generation finished successfully", "process.markdown.cancel": "Cancel", "process.markdown.save-draft": "Save Draft", "process.markdown.continue": "Continue", "process.markdown.building": "Building", "process.markdown.building.detail": "Slidev project is being built, please wait...", "process.markdown.build-failed": "Build Failed", "process.markdown.build-success": "Build Success", "process.markdown.building-preview": "Slidev Dev Server is launching, please wait...", "process.markdown.invalid-id": "Invalid ID", "process.markdown.error.get-preview-port": "Error getting preview port:", "process.markdown.loaded-existing": "Loaded Existing Project", "process.markdown.using-existing": "Using previously generated Slidev project", "process.markdown.error.invalid-id": "Slide ID is invalid", "process.markdown.error.fetch-failed": "Failed to fetch slide data", "process.markdown.error.init-conn": "Failed to initialize connection", "process.markdown.error.sse": "Failed to establish connection after multiple attempts. Please try again later.", "process.markdown.connection-issue": "Connection Issue", "process.markdown.connection-failed": "Connection Failed", "process.markdown.error.failed-parse": "Failed to parse server message", "process.markdown.error": "Error", "process.markdown.reconnect.attempt": "Attempting to reconnect ({0}/{1})...", "process.markdown.preview": "Preview Slides", "process.markdown.regenerate": "Regenerate", "process.markdown.deploy": "Deploy", "outline.panel.group": "Group", "outline.panel.content": "Content", "outline.panel.group-header": "Group: {0}", "outline.panel.collapse-all": "Collapse All", "outline.panel.expand-all": "Expand All", "process.outline.reconnect.attempt": "Attempting to reconnect ({0}/{1})...", "process.markdown.slide-busy": "Slide Busy", "process.markdown.tool-response": "Tool response received", "process.markdown.error.unknown": "Unknown error", "process.markdown.error.unknown-tool": "Unknown tool", "process.markdown.completed-success": "Markdown generation completed successfully!", "process.route.invalid-params": "Invalid parameters. Please provide a valid slide ID.", "process.route.invalid-stage": "Invalid stage. Valid stages are 'input', 'outline' and 'markdown'.", "nav.public-slides": "Public Slides", "nav.my-slides": "My Slides", "process.stage.user-input": "User Input", "process.stage.outline": "Outline", "process.stage.markdown": "PPT", "process.tooltip.finished": "Finished", "locale.english": "English", "locale.chinese-simplified": "Simplified Chinese", "dashboard.delete.confirm": "Delete Confirmation", "dashboard.delete.remind": "Are you sure you want to delete {0}?", "yes": "Yes", "no": "No", "not-ready": "Not Ready", "slide-is-not-ready": "Slide is not ready yet, click edit to deploy", "process.input.theme": "Theme", "process.input.theme.belong-to": "Theme Belongs To", "process.input.theme.help": "Help", "choose-theme": "Choose <PERSON>", "theme-preview": "Theme Preview", "theme": "Theme", "profile.username": "Username", "profile.email": "Email", "profile.website": "Website", "you-are-not-log-in": "You are not logged in.", "public-slides": "Public Slides", "loading-slides": "Loading slides", "no-public-slides-found": "No public slides found", "save-change": "Save Changes"}