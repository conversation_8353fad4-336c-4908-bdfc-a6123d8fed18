# UserId 类型不一致问题修复

## 问题描述
遇到错误：
```
QueryFailedError: SQLITE_CONSTRAINT: NOT NULL constraint failed: slides.userId
```

## 根本原因
数据库设计中存在类型不一致：
- `User.id` 是 `number` 类型（自增主键）
- `Slide.userId` 在代码中被错误定义为 `string` 类型
- 导致在创建 slide 时，userId 传递失败

## 已修复的文件

### 1. `backend/src/app/slides/slide.entity.ts`
```typescript
// 修复前
@Column()
userId!: string;

// 修复后  
@Column()
userId!: number;
```

### 2. `backend/src/app/slides/slides.service.ts`
```typescript
// 修复前
async createSlide(userId: string, createSlideDto: CreateSlideDto, file?: MulterFile)

// 修复后
async createSlide(userId: number, createSlideDto: CreateSlideDto, file?: MulterFile)
```

### 3. `backend/src/app/slides/slide.repository.ts`
```typescript
// 修复前
async findByUserId(userId: string, visibility: 'public' | 'private' | 'all' = 'all')
async getPublicSlides(userId: string | null, skip: number, take: number)
async remove(id: string)

// 修复后
async findByUserId(userId: number, visibility: 'public' | 'private' | 'all' = 'all')
async getPublicSlides(userId: number | null, skip: number, take: number)
async remove(id: number)
```

### 4. `backend/src/app/slides/slides.controller.ts`
```typescript
// 修复了类型转换
const userIdNumber = userId ? Number(userId) : null;
await this.slideRepository.remove(Number(id));
```

## 验证修复
重启服务后，创建幻灯片应该不再出现 userId 约束错误。

## 注意事项
- 数据库结构本身是正确的（userId 为 integer）
- 主要是 TypeScript 类型定义和代码逻辑的不一致
- 所有相关的类型都已统一为 `number`
